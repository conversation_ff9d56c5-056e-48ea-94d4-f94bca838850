#!/bin/bash

lenguajeOrigen="es"
lenguajeDestino="en"

# Mapear a Tesseract
case "$lenguajeOrigen" in
  es) langTesseract="spa" ;;
  en) langTesseract="eng" ;;
  fr) langTesseract="fra" ;;
  de) langTesseract="deu" ;;
  it) langTesseract="ita" ;;
  pt) langTesseract="por" ;;
  *) langTesseract="eng" ;;
esac

# Cerrar ventana si está abierta
eww close translate 2>/dev/null

# Ejecutar OCR y traducción paso a paso para debug
echo "Iniciando captura de pantalla..."
hyprshot --mode region --output-folder /tmp --filename captura_temp.png

if [ ! -f /tmp/captura_temp.png ]; then
    TRADUCCION="Error: No se pudo capturar la pantalla"
else
    echo "Ejecutando OCR..."
    TEXTO_OCR=$(tesseract /tmp/captura_temp.png stdout -l "$langTesseract" 2>/dev/null)

    if [ -z "$TEXTO_OCR" ]; then
        TRADUCCION="Error: No se pudo extraer texto de la imagen"
    else
        echo "Texto extraído: $TEXTO_OCR"
        echo "Traduciendo..."
        TRADUCCION=$(echo "$TEXTO_OCR" | trans "$lenguajeOrigen:$lenguajeDestino" -brief 2>/dev/null)

        if [ -z "$TRADUCCION" ]; then
            TRADUCCION="Error: No se pudo traducir el texto extraído: '$TEXTO_OCR'"
        fi
    fi

    # Limpiar archivo temporal
    rm -f /tmp/captura_temp.png
fi

# Actualizar la variable en EWW con la traducción y mostrar ventana
eww update traduccion="$TRADUCCION"
eww open translate

# Esperar y cerrar
sleep 40
eww close translate
